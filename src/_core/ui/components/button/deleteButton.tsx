"use client";

import React, { useState } from "react";
import { Stack, Typography } from "@mui/material";
import { Trash2 } from "react-feather";
import DeleteConfirmModal from "../CRUD/delete/deleteConfirmModalRenderer";

import { EntityKeys } from "@/_lib/utils/entities";

export default function DeleteButton({
  entityName,
  entityId,
  entityLabel,
  onDeleteSuccess,
}: {
  entityName: EntityKeys;
  entityId: string;
  entityLabel?: string;
  onDeleteSuccess?: () => void;
}) {
  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  return (
    <>
      {/* Trigger Button */}
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={handleOpen}
        sx={{ cursor: "pointer" }}
      >
        <Trash2 />
        <Typography
          sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
        >
          Eliminar
        </Typography>
      </Stack>

      {/* Confirmation Modal */}
      <DeleteConfirmModal
        open={open}
        onClose={handleClose}
        entityName={entityName}
        entityId={entityId}
        entityLabel={entityLabel}
        onDeleteSuccess={onDeleteSuccess}
      />
    </>
  );
}
