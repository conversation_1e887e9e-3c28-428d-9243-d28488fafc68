"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>u,
  MenuItem,
} from "@mui/material";
import { MoreVertical } from "react-feather";
import { useState } from "react";
import { ActionMeta } from "./types";

import DeleteButton from "@/_core/ui/components/button/deleteButton";

const renderers = {
  delete: DeleteButton,
  default: () => <></>,
} 

type Props = {
  actions: ActionMeta[];
};

export default function RowActionsMenu({ actions }: Props) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <IconButton onClick={handleOpen}>
        <MoreVertical size={20} />
      </IconButton>
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {actions.map((action) => {
          const renderer = action.renderer as keyof typeof renderers;
          const Component = renderers[renderer] || renderers.default;

          return (
            <MenuItem
              key={action.key}
              onMouseOut={handleClose}
            >
              <Component {...action.options} />
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
}
